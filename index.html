<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analoge Uhr - <PERSON> folgen</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
            cursor: none;
        }
        
        #clock {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1;
        }
        
        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="info">Bewegen Sie die Maus - die Uhr folgt!</div>
    <canvas id="clock"></canvas>

    <script>
        const canvas = document.getElementById('clock');
        const ctx = canvas.getContext('2d');
        const clockRadius = 90;

        let mouseX = window.innerWidth / 2;
        let mouseY = window.innerHeight / 2;
        let targetX = mouseX;
        let targetY = mouseY;

        // Partikel-System für die Uhr
        class Particle {
            constructor(x, y, type, size = 2, color = '#ffffff') {
                this.originalX = x;
                this.originalY = y;
                this.x = mouseX - 100 + x; // Initiale Position relativ zur Maus
                this.y = mouseY - 100 + y;
                this.vx = 0;
                this.vy = 0;
                this.type = type; // 'rim', 'hour-mark', 'minute-mark', 'hour-hand', 'minute-hand', 'second-hand', 'center'
                this.size = size;
                this.color = color;
                this.friction = 0.85;
                this.baseAcceleration = 0.15;
            }

            update() {
                // Zielposition basierend auf Mausposition berechnen
                const targetX = mouseX - 100 + this.originalX;
                const targetY = mouseY - 100 + this.originalY;

                // Entfernung zur Zielposition berechnen
                const dx = targetX - this.x;
                const dy = targetY - this.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                // Beschleunigung basierend auf Entfernung - nähere Punkte beschleunigen stärker
                // Je weiter weg, desto langsamer die Beschleunigung
                const distanceFactor = Math.max(0.1, Math.min(2.0, distance / 50));
                const acceleration = this.baseAcceleration * distanceFactor;

                this.vx += dx * acceleration;
                this.vy += dy * acceleration;

                // Reibung anwenden
                this.vx *= this.friction;
                this.vy *= this.friction;

                // Position aktualisieren
                this.x += this.vx;
                this.y += this.vy;
            }

            draw() {
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, 2 * Math.PI);
                ctx.fillStyle = this.color;
                ctx.fill();
            }
        }

        let particles = [];

        function createClockParticles() {
            particles = [];

            // Uhrenrand aus Punkten
            for (let i = 0; i < 120; i++) {
                const angle = (i * 2 * Math.PI) / 120;
                const x = 100 + Math.cos(angle) * clockRadius;
                const y = 100 + Math.sin(angle) * clockRadius;
                particles.push(new Particle(x, y, 'rim', 1.5, '#cccccc'));
            }

            // Stunden-Markierungen aus größeren Punkten
            for (let i = 0; i < 12; i++) {
                const angle = (i * Math.PI) / 6;
                for (let j = 0; j < 8; j++) {
                    const radius = clockRadius - 15 + j * 2;
                    const x = 100 + Math.cos(angle - Math.PI / 2) * radius;
                    const y = 100 + Math.sin(angle - Math.PI / 2) * radius;
                    particles.push(new Particle(x, y, 'hour-mark', 2, '#333333'));
                }
            }

            // Minuten-Markierungen aus kleineren Punkten
            for (let i = 0; i < 60; i++) {
                if (i % 5 !== 0) {
                    const angle = (i * Math.PI) / 30;
                    for (let j = 0; j < 3; j++) {
                        const radius = clockRadius - 10 + j * 2;
                        const x = 100 + Math.cos(angle - Math.PI / 2) * radius;
                        const y = 100 + Math.sin(angle - Math.PI / 2) * radius;
                        particles.push(new Particle(x, y, 'minute-mark', 1, '#666666'));
                    }
                }
            }
        }

        function createClockHands() {
            const now = new Date();
            const hours = now.getHours() % 12;
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();

            // Stundenzeiger aus Punkten
            const hourAngle = ((hours + minutes / 60) * Math.PI) / 6;
            for (let i = 0; i < 25; i++) {
                const radius = i * 1.6;
                const x = 100 + Math.cos(hourAngle - Math.PI / 2) * radius;
                const y = 100 + Math.sin(hourAngle - Math.PI / 2) * radius;
                const particle = new Particle(x, y, 'hour-hand', 2.5, '#333333');
                particle.handIndex = i;
                particle.handType = 'hour';
                particles.push(particle);
            }

            // Minutenzeiger aus Punkten
            const minuteAngle = (minutes * Math.PI) / 30;
            for (let i = 0; i < 35; i++) {
                const radius = i * 1.7;
                const x = 100 + Math.cos(minuteAngle - Math.PI / 2) * radius;
                const y = 100 + Math.sin(minuteAngle - Math.PI / 2) * radius;
                const particle = new Particle(x, y, 'minute-hand', 2, '#333333');
                particle.handIndex = i;
                particle.handType = 'minute';
                particles.push(particle);
            }

            // Sekundenzeiger aus Punkten
            const secondAngle = (seconds * Math.PI) / 30;
            for (let i = 0; i < 40; i++) {
                const radius = i * 1.75;
                const x = 100 + Math.cos(secondAngle - Math.PI / 2) * radius;
                const y = 100 + Math.sin(secondAngle - Math.PI / 2) * radius;
                const particle = new Particle(x, y, 'second-hand', 1.5, '#ff0000');
                particle.handIndex = i;
                particle.handType = 'second';
                particles.push(particle);
            }

            // Mittelpunkt aus mehreren Punkten
            for (let i = 0; i < 20; i++) {
                const angle = (i * 2 * Math.PI) / 20;
                const radius = Math.random() * 8;
                const x = 100 + Math.cos(angle) * radius;
                const y = 100 + Math.sin(angle) * radius;
                const particle = new Particle(x, y, 'center', 2, '#333333');
                particle.centerAngle = angle;
                particle.centerRadius = radius;
                particles.push(particle);
            }
        }

        function updateClockHands() {
            const now = new Date();
            const hours = now.getHours() % 12;
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();

            // Zeiger-Partikel aktualisieren statt neu zu erstellen
            particles.forEach(particle => {
                if (particle.handType === 'hour') {
                    const hourAngle = ((hours + minutes / 60) * Math.PI) / 6;
                    const radius = particle.handIndex * 1.6;
                    particle.originalX = 100 + Math.cos(hourAngle - Math.PI / 2) * radius;
                    particle.originalY = 100 + Math.sin(hourAngle - Math.PI / 2) * radius;
                } else if (particle.handType === 'minute') {
                    const minuteAngle = (minutes * Math.PI) / 30;
                    const radius = particle.handIndex * 1.7;
                    particle.originalX = 100 + Math.cos(minuteAngle - Math.PI / 2) * radius;
                    particle.originalY = 100 + Math.sin(minuteAngle - Math.PI / 2) * radius;
                } else if (particle.handType === 'second') {
                    const secondAngle = (seconds * Math.PI) / 30;
                    const radius = particle.handIndex * 1.75;
                    particle.originalX = 100 + Math.cos(secondAngle - Math.PI / 2) * radius;
                    particle.originalY = 100 + Math.sin(secondAngle - Math.PI / 2) * radius;
                } else if (particle.type === 'center' && particle.centerAngle !== undefined) {
                    particle.originalX = 100 + Math.cos(particle.centerAngle) * particle.centerRadius;
                    particle.originalY = 100 + Math.sin(particle.centerAngle) * particle.centerRadius;
                }
            });
        }

        // Mausposition verfolgen
        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });

        function animate() {
            // Canvas leeren
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Alle Partikel aktualisieren und zeichnen
            particles.forEach(particle => {
                particle.update();
                particle.draw();
            });

            requestAnimationFrame(animate);
        }

        // Initialisierung
        canvas.style.position = 'fixed';
        canvas.style.left = '0';
        canvas.style.top = '0';
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        createClockParticles();
        createClockHands();
        updateClockHands();

        // Zeiger jede Sekunde aktualisieren
        setInterval(updateClockHands, 1000);

        // Animation starten
        animate();

        // Canvas-Größe bei Fenstergrößenänderung anpassen
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
    </script>
</body>
</html>
