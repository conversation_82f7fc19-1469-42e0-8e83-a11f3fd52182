<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analoge <PERSON> - <PERSON> folgen</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
            cursor: none;
        }
        
        #clock {
            position: absolute;
            border-radius: 50%;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
            transform: translate(-50%, -50%);
            transition: all 0.1s ease-out;
        }
        
        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="info">Bewegen Sie die Maus - die Uhr folgt!</div>
    <canvas id="clock" width="200" height="200"></canvas>

    <script>
        const canvas = document.getElementById('clock');
        const ctx = canvas.getContext('2d');
        const clockRadius = 90;
        
        let mouseX = window.innerWidth / 2;
        let mouseY = window.innerHeight / 2;
        
        // Mausposition verfolgen
        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
            updateClockPosition();
        });
        
        function updateClockPosition() {
            canvas.style.left = mouseX + 'px';
            canvas.style.top = mouseY + 'px';
        }
        
        function drawClock() {
            const now = new Date();
            const hours = now.getHours() % 12;
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            
            // Canvas leeren
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Uhrengehäuse
            ctx.beginPath();
            ctx.arc(100, 100, clockRadius, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();
            ctx.strokeStyle = '#333333';
            ctx.lineWidth = 4;
            ctx.stroke();
            
            // Stunden-Markierungen
            ctx.strokeStyle = '#333333';
            ctx.lineWidth = 3;
            for (let i = 0; i < 12; i++) {
                const angle = (i * Math.PI) / 6;
                const x1 = 100 + Math.cos(angle - Math.PI / 2) * (clockRadius - 15);
                const y1 = 100 + Math.sin(angle - Math.PI / 2) * (clockRadius - 15);
                const x2 = 100 + Math.cos(angle - Math.PI / 2) * (clockRadius - 5);
                const y2 = 100 + Math.sin(angle - Math.PI / 2) * (clockRadius - 5);
                
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
            
            // Minuten-Markierungen
            ctx.strokeStyle = '#666666';
            ctx.lineWidth = 1;
            for (let i = 0; i < 60; i++) {
                if (i % 5 !== 0) { // Nur die Markierungen, die keine Stunden sind
                    const angle = (i * Math.PI) / 30;
                    const x1 = 100 + Math.cos(angle - Math.PI / 2) * (clockRadius - 10);
                    const y1 = 100 + Math.sin(angle - Math.PI / 2) * (clockRadius - 10);
                    const x2 = 100 + Math.cos(angle - Math.PI / 2) * (clockRadius - 5);
                    const y2 = 100 + Math.sin(angle - Math.PI / 2) * (clockRadius - 5);
                    
                    ctx.beginPath();
                    ctx.moveTo(x1, y1);
                    ctx.lineTo(x2, y2);
                    ctx.stroke();
                }
            }
            
            // Stundenzeiger
            const hourAngle = ((hours + minutes / 60) * Math.PI) / 6;
            ctx.beginPath();
            ctx.moveTo(100, 100);
            ctx.lineTo(
                100 + Math.cos(hourAngle - Math.PI / 2) * 40,
                100 + Math.sin(hourAngle - Math.PI / 2) * 40
            );
            ctx.strokeStyle = '#333333';
            ctx.lineWidth = 6;
            ctx.lineCap = 'round';
            ctx.stroke();
            
            // Minutenzeiger
            const minuteAngle = (minutes * Math.PI) / 30;
            ctx.beginPath();
            ctx.moveTo(100, 100);
            ctx.lineTo(
                100 + Math.cos(minuteAngle - Math.PI / 2) * 60,
                100 + Math.sin(minuteAngle - Math.PI / 2) * 60
            );
            ctx.strokeStyle = '#333333';
            ctx.lineWidth = 4;
            ctx.lineCap = 'round';
            ctx.stroke();
            
            // Sekundenzeiger
            const secondAngle = (seconds * Math.PI) / 30;
            ctx.beginPath();
            ctx.moveTo(100, 100);
            ctx.lineTo(
                100 + Math.cos(secondAngle - Math.PI / 2) * 70,
                100 + Math.sin(secondAngle - Math.PI / 2) * 70
            );
            ctx.strokeStyle = '#ff0000';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.stroke();
            
            // Mittelpunkt
            ctx.beginPath();
            ctx.arc(100, 100, 8, 0, 2 * Math.PI);
            ctx.fillStyle = '#333333';
            ctx.fill();
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.stroke();
        }
        
        // Initiale Position setzen
        updateClockPosition();
        
        // Uhr alle Sekunde aktualisieren
        setInterval(drawClock, 1000);
        
        // Erste Zeichnung
        drawClock();
    </script>
</body>
</html>
